<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Notes</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="styles.css" />
    </head>
    <body class="bg-zinc-900">
        <div class="container mx-auto p-4 min-h-screen ">
            <form action="/create" method="POST" class="form">
                <input
                    type="text"
                    name="title"
                    placeholder="Title"
                    required
                    class="w-full p-2 mb-4 bg-zinc-800 text-white rounded-lg"
                />
                <textarea
                    name="note"
                    placeholder="Note"
                    required
                    class="w-full p-2 mb-4 bg-zinc-800 text-white rounded-lg resize-none"
                ></textarea>
                <input
                    type="submit"
                    value="Create Task"
                    class="border border-grey-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-zinc-700 cursor-pointer"
                />
            </form>

            <% if (files.length > 0) { %>
                <div class="task flex py-12 gap-4 justify-center flex-wrap items-center">
                    <% files.forEach(function(file) { %>
                        <div class="task-item bg-zinc-800 py-12 rounded-lg mb-4 px-4">
                            <h2 class="text-xl font-bold mb-2 text-white"><%= file %></h2>
                            <p class="text-sm mb-2 text-gray-300">Task file</p>
                            <button
                                class="border border-grey-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-zinc-700"
                            >
                                Read More
                            </button>
                        </div>
                    <% }); %>
                </div>
            <% } else { %>
                <div class="task flex py-12 gap-4 justify-center flex-wrap items-center">
                    <h3 class="text-xl font-bold mb-2 text-white">
                        No Tasks Found
                    </h3>
                </div>
            <% } %>
        </div>
    </body>
</html>
