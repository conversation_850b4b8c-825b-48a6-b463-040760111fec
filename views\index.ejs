<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Notes</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="styles.css" />
    </head>
    <body class="bg-zinc-900">
        <div class="container mx-auto p-4 min-h-screen">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-white mb-2">Task Manager</h1>
                <p class="text-gray-400">Create and manage your tasks efficiently</p>
            </div>

            <!-- Create Task Form -->
            <div class="max-w-2xl mx-auto mb-12">
                <div class="bg-zinc-800 rounded-xl p-8 shadow-lg border border-zinc-700">
                    <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create New Task
                    </h2>
                    <form action="/create" method="POST" class="space-y-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Task Title</label>
                            <input
                                type="text"
                                id="title"
                                name="title"
                                placeholder="Enter task title..."
                                required
                                class="w-full p-4 bg-zinc-700 text-white rounded-lg border border-zinc-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all duration-200"
                            />
                        </div>
                        <div>
                            <label for="note" class="block text-sm font-medium text-gray-300 mb-2">Task Description</label>
                            <textarea
                                id="note"
                                name="note"
                                placeholder="Enter task description..."
                                required
                                rows="4"
                                class="w-full p-4 bg-zinc-700 text-white rounded-lg border border-zinc-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all duration-200 resize-none"
                            ></textarea>
                        </div>
                        <button
                            type="submit"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create Task
                        </button>
                    </form>
                </div>
            </div>

            <% if (files.length > 0) { %>
                <div class="py-12">
                    <h2 class="text-2xl font-bold text-white mb-8 text-center">Your Tasks</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                        <% files.forEach(function(file) { %>
                            <div class="task-item bg-zinc-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:transform hover:scale-105 border border-zinc-700 hover:border-zinc-600">
                                <!-- Task Header -->
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                                        <span class="text-xs text-gray-400 uppercase tracking-wide">Task</span>
                                    </div>
                                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>

                                <!-- Task Title -->
                                <h3 class="text-lg font-bold mb-3 text-white line-clamp-2 leading-tight">
                                    <%= file.replace('.txt', '').replace(/_/g, ' ') %>
                                </h3>

                                <!-- Task Preview -->
                                <p class="text-sm text-gray-400 mb-6 line-clamp-3">
                                    Click "Read More" to view the full task details and notes.
                                </p>

                                <!-- Action Button -->
                                <div class="flex items-center justify-between">
                                    <a href="/task/<%= file %>"
                                       class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 text-sm">
                                        <span>Read More</span>
                                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                    <span class="text-xs text-gray-500">
                                        <%= file.split('.')[0].length > 10 ? file.split('.')[0].substring(0, 10) + '...' : file.split('.')[0] %>
                                    </span>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                </div>
            <% } else { %>
                <div class="task flex py-12 gap-4 justify-center flex-wrap items-center">
                    <h3 class="text-xl font-bold mb-2 text-white">
                        No Tasks Found
                    </h3>
                </div>
            <% } %>
        </div>
    </body>
</html>
