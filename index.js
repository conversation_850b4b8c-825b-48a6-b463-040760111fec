const express = require('express');
const app = express();
const port = 3000;
const path = require('path');
const fs = require('fs');

app.use(express.json())
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));
app.set('view engine', 'ejs');
app.get('/', (req, res) => {
 fs.readdir('./files', (err, files) => {
  if (err) {
    console.log(err);
    // If files directory doesn't exist, render with empty array
    res.render('index', { files: [] });
    return;
  }
  res.render('index', { files: files });
})
});

app.post('/create', (req, res) => {
  const { title, note } = req.body;

  // Create filename from title (replace spaces with underscores and remove special characters)
  const filename = title.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_') + '.txt';

  // Create file content
  const fileContent = `Title: ${title}\nNote: ${note}\nCreated: ${new Date().toISOString()}`;

  // Write file to files directory
  fs.writeFile(`./files/${filename}`, fileContent, (err) => {
    if (err) {
      console.log('Error creating file:', err);
      return res.status(500).send('Error creating task');
    }

    console.log(`Task created: ${filename}`);
    // Redirect back to home page to show the new task
    res.redirect('/');
  });
});

app.get('/task/:filename', (req, res) => {
  const filename = req.params.filename;

  fs.readFile(`./files/${filename}`, 'utf8', (err, data) => {
    if (err) {
      console.log('Error reading file:', err);
      return res.status(404).send(`
        <html>
          <head><title>Task Not Found</title></head>
          <body style="font-family: Arial; text-align: center; padding: 50px; background: #18181b; color: white;">
            <h1>Task Not Found</h1>
            <p>The requested task could not be found.</p>
            <a href="/" style="color: #3b82f6; text-decoration: none;">← Back to Tasks</a>
          </body>
        </html>
      `);
    }

    // Check if file is empty or has no content
    if (!data || data.trim() === '') {
      return res.render('task', {
        title: filename.replace('.txt', '').replace(/_/g, ' '),
        note: 'This task file is empty or corrupted.',
        created: new Date().toISOString(),
        filename: filename
      });
    }

    // Parse the file content with error handling
    const lines = data.split('\n').filter(line => line.trim() !== '');

    let title = filename.replace('.txt', '').replace(/_/g, ' ');
    let note = 'No description available';
    let created = new Date().toISOString();

    // Safely parse each line
    if (lines.length > 0 && lines[0].startsWith('Title: ')) {
      title = lines[0].replace('Title: ', '');
    } else if (lines.length > 0) {
      // If first line doesn't have "Title: " prefix, use it as title
      title = lines[0];
    }

    if (lines.length > 1 && lines[1].startsWith('Note: ')) {
      note = lines[1].replace('Note: ', '');
    } else if (lines.length > 1) {
      // If second line doesn't have "Note: " prefix, use it as note
      note = lines[1];
    }

    if (lines.length > 2 && lines[2].startsWith('Created: ')) {
      created = lines[2].replace('Created: ', '');
    }

    res.render('task', {
      title: title,
      note: note,
      created: created,
      filename: filename
    });
  });
});

// app.get('/home', (req, res) => {
//   res.render('index');
// });

app.listen(port, () => {
  console.log(`Example app listening at http://localhost:${port}`);
});