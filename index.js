const express = require('express');
const app = express();
const port = 3000;
const path = require('path');
const fs = require('fs');

app.use(express.json())
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));
app.set('view engine', 'ejs');
app.get('/', (req, res) => {
 fs.readdir('./files', (err, files) => {
  if (err) {
    console.log(err);
    // If files directory doesn't exist, render with empty array
    res.render('index', { files: [] });
    return;
  }
  res.render('index', { files: files });
})
});

app.post('/create', (req, res) => {
  const { title, note } = req.body;

  // Create filename from title (replace spaces with underscores and remove special characters)
  const filename = title.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_') + '.txt';

  // Create file content
  const fileContent = `Title: ${title}\nNote: ${note}\nCreated: ${new Date().toISOString()}`;

  // Write file to files directory
  fs.writeFile(`./files/${filename}`, fileContent, (err) => {
    if (err) {
      console.log('Error creating file:', err);
      return res.status(500).send('Error creating task');
    }

    console.log(`Task created: ${filename}`);
    // Redirect back to home page to show the new task
    res.redirect('/');
  });
});

// app.get('/home', (req, res) => {
//   res.render('index');
// });

app.listen(port, () => {
  console.log(`Example app listening at http://localhost:${port}`);
});