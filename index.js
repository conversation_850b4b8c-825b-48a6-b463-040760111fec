const express = require('express');
const app = express();
const port = 3000;
const path = require('path');
const fs = require('fs');

app.use(express.json())
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));
app.set('view engine', 'ejs');
app.get('/', (req, res) => {
 fs.readdir('./files', (err, files) => {
  if (err) {
    console.log(err);
    return;
  }
  res.render('index', { files: files });
})
});
// app.get('/home', (req, res) => {
//   res.render('index');
// });

app.listen(port, () => {
  console.log(`Example app listening at http://localhost:${port}`);
});