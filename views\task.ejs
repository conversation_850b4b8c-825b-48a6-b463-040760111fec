<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title><%= title %> - Task Details</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="styles.css" />
    </head>
    <body class="bg-zinc-900 text-white">
        <div class="container mx-auto p-4 min-h-screen">
            <!-- Header with back button -->
            <div class="mb-8">
                <a href="/" class="inline-flex items-center text-blue-400 hover:text-blue-300 mb-4">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Back to Tasks
                </a>
                <h1 class="text-3xl font-bold text-white">Task Details</h1>
            </div>

            <!-- Task Detail Card -->
            <div class="bg-zinc-800 rounded-lg p-8 shadow-lg max-w-4xl mx-auto">
                <!-- Task Title -->
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2"><%= title %></h2>
                    <div class="flex items-center text-gray-400 text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Created: <%= new Date(created).toLocaleDateString() %> at <%= new Date(created).toLocaleTimeString() %>
                    </div>
                </div>

                <!-- Task Content -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-300 mb-3">Note:</h3>
                    <div class="bg-zinc-700 rounded-lg p-4">
                        <p class="text-gray-200 leading-relaxed whitespace-pre-wrap"><%= note %></p>
                    </div>
                </div>

                <!-- File Info -->
                <div class="border-t border-zinc-600 pt-6">
                    <div class="flex items-center text-gray-400 text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        File: <%= filename %>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-4 mt-8">
                    <a href="/" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                        Back to All Tasks
                    </a>
                    <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200" onclick="deleteTask()">
                        Delete Task
                    </button>
                </div>
            </div>
        </div>

        <script>
            function deleteTask() {
                if (confirm('Are you sure you want to delete this task?')) {
                    // You can implement delete functionality here
                    alert('Delete functionality can be implemented');
                }
            }
        </script>
    </body>
</html>
